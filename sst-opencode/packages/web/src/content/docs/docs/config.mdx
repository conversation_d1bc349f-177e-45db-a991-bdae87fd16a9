---
title: Config
description: Using the opencode JSON config.
---

You can configure opencode using a JSON config file that can be placed in:

- Globally under `~/.config/opencode/config.json`.
- Your project root under `opencode.json`. This is safe to be checked into Git and uses the same schema as the global one.

```json
{
  "$schema": "https://opencode.ai/config.json",
  "theme": "opencode",
  "model": "anthropic/claude-sonnet-4-20250514",
  "autoshare": false,
  "autoupdate": true
}
```

In most cases, you'll want to use the global config for things like themes, providers, or keybinds. Having a config per project is useful if you are using different providers for your company.

When opencode starts up, it looks for a config file in the current directory or traverse up to the nearest Git directory.

---

## Schema

The config file has a schema that's defined in [**`opencode.ai/config.json`**](https://opencode.ai/config.json).

Your editor should be able to validate and autocomplete based on the schema.

---

### Models

You can configure the providers and models you want to use in your opencode config through the `provider` and `model` options.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "provider": { },
  "model": ""
}
```

[Learn more here](/docs/models).

---

### Themes

You can configure the theme you want to use in your opencode config through the `theme` option.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "theme": ""
}
```

[Learn more here](/docs/themes).

---

### Keybinds

You can customize your keybinds through the `keybinds` option.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "keybinds": { }
}
```

[Learn more here](/docs/keybinds).

---

### MCP servers

You can configure MCP servers you want to use through the `mcp` option.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "mcp": { }
}
```

[Learn more here](/docs/mcp-servers).

---

### Disabled providers

You can disable providers that are loaded automatically through the `disabled_providers` option. This is useful when you want to prevent certain providers from being loaded even if their credentials are available.

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "disabled_providers": ["openai", "gemini"]
}
```

The `disabled_providers` option accepts an array of provider IDs. When a provider is disabled:
- It won't be loaded even if environment variables are set
- It won't be loaded even if API keys are configured through `opencode auth login`
- The provider's models won't appear in the model selection list
