---
title: LSP servers
---

opencode integrates with _Language Server Protocol_, or LSP to improve how the LLM interacts with your codebase.

LSP servers for different languages give the LLM:

- **Diagnostics**: These include things like errors and lint warnings. So the LLM can generate code that has fewer mistakes without having to run the code.
- **Quick actions**: The LSP can allow the LLM to better navigate the codebase through features like _go-to-definition_ and _find references_.

## Auto-detection

By default, opencode will **automatically detect** the languages used in your project and add the right LSP servers.

## Manual configuration

You can also manually configure LSP servers by adding them under the `lsp` section in your opencode config.

```json title="opencode.json"
{
  "lsp": {
    "go": {
      "disabled": false,
      "command": "gopls"
    },
    "typescript": {
      "disabled": false,
      "command": "typescript-language-server",
      "args": ["--stdio"]
    }
  }
}
```
