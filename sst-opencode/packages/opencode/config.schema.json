{"type": "object", "properties": {"$schema": {"type": "string", "description": "JSON schema reference for configuration validation"}, "theme": {"type": "string", "description": "Theme name to use for the interface"}, "keybinds": {"type": "object", "properties": {"leader": {"type": "string", "description": "Leader key for keybind combinations"}, "help": {"type": "string", "description": "Show help dialog"}, "editor_open": {"type": "string", "description": "Open external editor"}, "session_new": {"type": "string", "description": "Create a new session"}, "session_list": {"type": "string", "description": "List all sessions"}, "session_share": {"type": "string", "description": "Share current session"}, "session_interrupt": {"type": "string", "description": "Interrupt current session"}, "session_compact": {"type": "string", "description": "Toggle compact mode for session"}, "tool_details": {"type": "string", "description": "Show tool details"}, "model_list": {"type": "string", "description": "List available models"}, "theme_list": {"type": "string", "description": "List available themes"}, "project_init": {"type": "string", "description": "Initialize project configuration"}, "input_clear": {"type": "string", "description": "Clear input field"}, "input_paste": {"type": "string", "description": "Paste from clipboard"}, "input_submit": {"type": "string", "description": "Submit input"}, "input_newline": {"type": "string", "description": "Insert newline in input"}, "history_previous": {"type": "string", "description": "Navigate to previous history item"}, "history_next": {"type": "string", "description": "Navigate to next history item"}, "messages_page_up": {"type": "string", "description": "Scroll messages up by one page"}, "messages_page_down": {"type": "string", "description": "Scroll messages down by one page"}, "messages_half_page_up": {"type": "string", "description": "Scroll messages up by half page"}, "messages_half_page_down": {"type": "string", "description": "Scroll messages down by half page"}, "messages_previous": {"type": "string", "description": "Navigate to previous message"}, "messages_next": {"type": "string", "description": "Navigate to next message"}, "messages_first": {"type": "string", "description": "Navigate to first message"}, "messages_last": {"type": "string", "description": "Navigate to last message"}, "app_exit": {"type": "string", "description": "Exit the application"}}, "additionalProperties": false, "description": "Custom keybind configurations"}, "autoshare": {"type": "boolean", "description": "Share newly created sessions automatically"}, "autoupdate": {"type": "boolean", "description": "Automatically update to the latest version"}, "disabled_providers": {"type": "array", "items": {"type": "string"}, "description": "Disable providers that are loaded automatically"}, "model": {"type": "string", "description": "Model to use in the format of provider/model, eg anthropic/claude-2"}, "provider": {"type": "object", "additionalProperties": {"type": "object", "properties": {"api": {"type": "string"}, "name": {"type": "string"}, "env": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "npm": {"type": "string"}, "models": {"type": "object", "additionalProperties": {"type": "object", "properties": {"name": {"type": "string"}, "attachment": {"type": "boolean"}, "reasoning": {"type": "boolean"}, "temperature": {"type": "boolean"}, "tool_call": {"type": "boolean"}, "cost": {"type": "object", "properties": {"input": {"type": "number"}, "output": {"type": "number"}, "cache_read": {"type": "number"}, "cache_write": {"type": "number"}}, "required": ["input", "output"], "additionalProperties": false}, "limit": {"type": "object", "properties": {"context": {"type": "number"}, "output": {"type": "number"}}, "required": ["context", "output"], "additionalProperties": false}, "id": {"type": "string"}, "options": {"type": "object", "additionalProperties": {}}}, "additionalProperties": false}}, "options": {"type": "object", "additionalProperties": {}}}, "required": ["models"], "additionalProperties": false}, "description": "Custom provider configurations and model overrides"}, "mcp": {"type": "object", "additionalProperties": {"anyOf": [{"type": "object", "properties": {"type": {"type": "string", "const": "local", "description": "Type of MCP server connection"}, "command": {"type": "array", "items": {"type": "string"}, "description": "Command and arguments to run the MCP server"}, "environment": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Environment variables to set when running the MCP server"}, "enabled": {"type": "boolean", "description": "Enable or disable the MCP server on startup"}}, "required": ["type", "command"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"type": "string", "const": "remote", "description": "Type of MCP server connection"}, "url": {"type": "string", "description": "URL of the remote MCP server"}, "enabled": {"type": "boolean", "description": "Enable or disable the MCP server on startup"}}, "required": ["type", "url"], "additionalProperties": false}]}, "description": "MCP (Model Context Protocol) server configurations"}, "instructions": {"type": "array", "items": {"type": "string"}, "description": "Additional instruction files or patterns to include"}, "experimental": {"type": "object", "properties": {"hook": {"type": "object", "properties": {"file_edited": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "array", "items": {"type": "string"}}, "environment": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["command"], "additionalProperties": false}}}, "session_completed": {"type": "array", "items": {"type": "object", "properties": {"command": {"type": "array", "items": {"type": "string"}}, "environment": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["command"], "additionalProperties": false}}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}