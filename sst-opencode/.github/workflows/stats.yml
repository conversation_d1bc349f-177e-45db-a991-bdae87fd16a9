name: stats

on:
  schedule:
    - cron: "0 12 * * *" # Run daily at 12:00 UTC
  workflow_dispatch: # Allow manual trigger

jobs:
  stats:
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Run stats script
        run: bun scripts/stats.ts

      - name: Commit stats
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add STATS.md
          git diff --staged --quiet || git commit -m "Update download stats $(date -I)"
          git push
