#!/usr/bin/env node

// Simple Node.js runner for sst-opencode
// This bypasses Bun dependencies to get basic functionality working

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

const cli = yargs(hideBin(process.argv))
  .scriptName("opencode")
  .help("help", "show help")
  .version("version", "show version number", "0.1.194")
  .alias("version", "v")
  .option("print-logs", {
    describe: "print logs to stderr",
    type: "boolean",
  })
  .usage(`
   ___                   ___          _      
  / _ \\ _ __   ___ _ __ / __\\___   __| | ___ 
 | | | | '_ \\ / _ \\ '_ / /  / _ \\ / _\` |/ _ \\
 | |_| | |_) |  __/ | / /__| (_) | (_| |  __/
  \\___/| .__/ \\___|_| \\____/\\___/ \\__,_|\\___|
       |_|                                   

AI coding agent, built for the terminal.
`)
  .command({
    command: '$0 [project]',
    describe: 'start opencode tui',
    builder: (yargs) =>
      yargs.positional('project', {
        type: 'string',
        describe: 'path to start opencode in',
      }),
    handler: async (args) => {
      console.log('🚀 Starting OpenCode TUI...');
      console.log('📁 Project directory:', args.project || process.cwd());
      console.log('');
      console.log('⚠️  Note: This is a simplified Node.js runner.');
      console.log('   For full functionality, please use Bun:');
      console.log('   bun run packages/opencode/src/index.ts');
      console.log('');
      console.log('🔧 Available commands:');
      console.log('   • help     - Show this help message');
      console.log('   • version  - Show version information');
      console.log('   • run      - Run OpenCode (simplified)');
      console.log('   • models   - List available models');
      console.log('   • auth     - Authentication commands');
      console.log('');
      console.log('💡 To get started with the full version:');
      console.log('   1. Install Bun: curl -fsSL https://bun.sh/install | bash');
      console.log('   2. Run: bun install');
      console.log('   3. Run: bun run dev');
    }
  })
  .command({
    command: 'run [prompt]',
    describe: 'run opencode with a prompt',
    builder: (yargs) =>
      yargs.positional('prompt', {
        type: 'string',
        describe: 'the prompt to run',
      }),
    handler: async (args) => {
      console.log('🤖 Running OpenCode with prompt:', args.prompt || 'interactive mode');
      console.log('');
      console.log('⚠️  This is a simplified runner. For full AI functionality,');
      console.log('   please use the full Bun version.');
    }
  })
  .command({
    command: 'models',
    describe: 'list available models',
    handler: async () => {
      console.log('🧠 Available Models:');
      console.log('');
      console.log('• OpenAI GPT-4');
      console.log('• Anthropic Claude');
      console.log('• DeepSeek (to be implemented)');
      console.log('');
      console.log('⚠️  For full model configuration, use the Bun version.');
    }
  })
  .command({
    command: 'auth',
    describe: 'authentication commands',
    handler: async () => {
      console.log('🔐 Authentication:');
      console.log('');
      console.log('Set your API keys as environment variables:');
      console.log('• OPENAI_API_KEY=your_key_here');
      console.log('• ANTHROPIC_API_KEY=your_key_here');
      console.log('');
      console.log('⚠️  For full auth functionality, use the Bun version.');
    }
  })
  .fail((msg) => {
    if (
      msg.startsWith("Unknown argument") ||
      msg.startsWith("Not enough non-option arguments")
    ) {
      cli.showHelp("log");
    }
  })
  .strict();

try {
  await cli.parse();
} catch (e) {
  console.error('Error:', e.message);
  process.exitCode = 1;
}
