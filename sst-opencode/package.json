{"$schema": "https://json.schemastore.org/package.json", "name": "opencode", "private": true, "type": "module", "packageManager": "bun@1.2.14", "scripts": {"dev": "bun run packages/opencode/src/index.ts", "typecheck": "bun run --filter='*' typecheck", "stainless": "bun run ./packages/opencode/src/index.ts serve ", "postinstall": "./scripts/hooks"}, "workspaces": {"packages": ["packages/*"], "catalog": {"typescript": "5.8.2", "@types/node": "22.13.9", "zod": "3.24.2", "ai": "4.3.16"}}, "devDependencies": {"prettier": "3.5.3", "sst": "3.17.8"}, "repository": {"type": "git", "url": "https://github.com/sst/opencode"}, "license": "MIT", "prettier": {"semi": false}, "overrides": {"zod": "3.24.2"}, "trustedDependencies": ["esbuild", "protobufjs", "sharp"], "patchedDependencies": {"ai@4.3.16": "patches/<EMAIL>"}}