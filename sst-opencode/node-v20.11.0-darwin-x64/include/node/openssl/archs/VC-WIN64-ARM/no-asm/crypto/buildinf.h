/*
 * WARNING: do not edit!
 * Generated by util/mkbuildinf.pl
 *
 * Copyright 2014-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define PLATFORM "platform: VC-WIN64-ARM"
#define DATE "built on: Thu Oct 26 15:05:18 2023 UTC"

/*
 * Generate compiler_flags as an array of individual characters. This is a
 * workaround for the situation where CFLAGS gets too long for a C90 string
 * literal
 */
static const char compiler_flags[] = {
    'c','o','m','p','i','l','e','r',':',' ','c','l',' ','/','Z','i',
    ' ','/','F','d','o','s','s','l','_','s','t','a','t','i','c','.',
    'p','d','b',' ','/','G','s','0',' ','/','G','F',' ','/','G','y',
    ' ','/','M','D',' ','/','W','3',' ','/','w','d','4','0','9','0',
    ' ','/','n','o','l','o','g','o',' ','/','O','2',' ','-','D','L',
    '_','E','N','D','I','A','N',' ','-','D','O','P','E','N','S','S',
    'L','_','P','I','C','\0'
};
