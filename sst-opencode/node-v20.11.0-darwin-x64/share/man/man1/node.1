.\"
.\" This manpage is written in mdoc(7).
.\"
.\" * Language reference:
.\"   https://man.openbsd.org/mdoc.7
.\"
.\" * Atom editor support:
.\"   https://atom.io/packages/language-roff
.\"
.\" * Linting changes:
.\"   mandoc -Wall -Tlint /path/to/this.file  # BSD
.\"   groff -w all -z /path/to/this.file      # GNU/Linux, macOS
.\"
.\"
.\" Before making changes, please note the following:
.\"
.\" * In Roff, each new sentence should begin on a new line. This gives
.\"   the Roff formatter better control over text-spacing, line-wrapping,
.\"   and paragraph justification.
.\"
.\" * Do not leave blank lines in the markup. If whitespace is desired
.\"   for readability, put a dot in the first column to indicate a null/empty
.\"   command. Comments and horizontal whitespace may optionally follow: each
.\"   of these lines are an example of a null command immediately followed by
.\"   a comment.
.\"
.\"======================================================================
.
.tr -\-^\(ha~\(ti`\(ga
.Dd 2018
.Dt NODE 1
.
.Sh NAME
.Nm node
.Nd server-side JavaScript runtime
.
.\"======================================================================
.Sh SYNOPSIS
.Nm node
.Op Ar options
.Op Ar v8-options
.Op Fl e Ar string | Ar script.js | Fl
.Op Fl -
.Op Ar arguments ...
.
.Nm node
.Cm inspect
.Op Fl e Ar string | Ar script.js | Fl | Ar <host>:<port>
.Ar ...
.
.Nm node
.Op Fl -v8-options
.
.\"======================================================================
.Sh DESCRIPTION
Node.js is a set of libraries for JavaScript which allows it to be used outside of the browser.
It is primarily focused on creating simple, easy-to-build network clients and servers.
.Pp
Execute
.Nm
without arguments to start a REPL.
.
.Sh OPTIONS
.Bl -tag -width 6n
.It Sy -
Alias for stdin, analogous to the use of - in other command-line utilities.
The executed script is read from stdin, and remaining arguments are passed to the script.
.
.It Fl -
Indicate the end of command-line options.
Pass the rest of the arguments to the script.
.Pp
If no script filename or eval/print script is supplied prior to this, then
the next argument will be used as a script filename.
.
.It Fl -abort-on-uncaught-exception
Aborting instead of exiting causes a core file to be generated for analysis.
.
.It Fl -allow-fs-read
Allow file system read access when using the permission model.
.
.It Fl -allow-fs-write
Allow file system write access when using the permission model.
.
.It Fl -allow-child-process
Allow spawning process when using the permission model.
.
.It Fl -allow-worker
Allow creating worker threads when using the permission model.
.
.It Fl -completion-bash
Print source-able bash completion script for Node.js.
.
.It Fl C , Fl -conditions Ar string
Use custom conditional exports conditions.
.Ar string
.
.It Fl -cpu-prof
Start the V8 CPU profiler on start up, and write the CPU profile to disk
before exit. If
.Fl -cpu-prof-dir
is not specified, the profile will be written to the current working directory
with a generated file name.
.
.It Fl -cpu-prof-dir
The directory where the CPU profiles generated by
.Fl -cpu-prof
will be placed.
The default value is controlled by the
.Fl -diagnostic-dir .
command-line option.
.
.It Fl -cpu-prof-interval
The sampling interval in microseconds for the CPU profiles generated by
.Fl -cpu-prof .
The default is
.Sy 1000 .
.
.It Fl -cpu-prof-name
File name of the V8 CPU profile generated with
.Fl -cpu-prof .
.
.It Fl -diagnostic-dir
Set the directory for all diagnostic output files.
Default is current working directory.
Set the directory to which all diagnostic output files will be written to.
Defaults to current working directory.
.
Affects the default output directory of:
.Fl -cpu-prof-dir .
.Fl -heap-prof-dir .
.Fl -redirect-warnings .
.
.It Fl -disable-proto Ns = Ns Ar mode
Disable the `Object.prototype.__proto__` property. If
.Ar mode
is `delete`, the property will be removed entirely. If
.Ar mode
is `throw`, accesses to the property will throw an exception with the code
`ERR_PROTO_ACCESS`.
.
.It Fl -disallow-code-generation-from-strings
Make built-in language features like `eval` and `new Function` that generate
code from strings throw an exception instead. This does not affect the Node.js
`vm` module.
.
.It Fl -enable-fips
Enable FIPS-compliant crypto at startup.
Requires Node.js to be built with
.Sy ./configure --openssl-fips .
.
.It Fl -enable-source-maps
Enable Source Map V3 support for stack traces.
.
.It Fl -experimental-default-type Ns = Ns Ar type
Interpret as either ES modules or CommonJS modules input via --eval or STDIN, when --input-type is unspecified;
.js or extensionless files with no sibling or parent package.json;
.js or extensionless files whose nearest parent package.json lacks a "type" field, unless under node_modules.
.
.It Fl -experimental-global-webcrypto
Expose the Web Crypto API on the global scope.
.
.It Fl -experimental-import-meta-resolve
Enable experimental ES modules support for import.meta.resolve().
.
.It Fl -experimental-loader Ns = Ns Ar module
Specify the
.Ar module
to use as a custom module loader.
.
.It Fl -experimental-network-imports
Enable experimental support for loading modules using `import` over `https:`.
.
.It Fl -experimental-permission
Enable the experimental permission model.
.
.It Fl -experimental-policy
Use the specified file as a security policy.
.
.It Fl -experimental-shadow-realm
Use this flag to enable ShadowRealm support.
.
.It Fl -experimental-test-coverage
Enable code coverage in the test runner.
.
.It Fl -experimental-websocket
Enable experimental support for the WebSocket API.
.
.It Fl -no-experimental-fetch
Disable experimental support for the Fetch API.
.
.It Fl -no-experimental-global-customevent
Disable exposition of the CustomEvent on the global scope.
.
.It Fl -no-experimental-global-webcrypto
Disable exposition of the Web Crypto API on the global scope.
.
.It Fl -no-experimental-repl-await
Disable top-level await keyword support in REPL.
.
.It Fl -experimental-vm-modules
Enable experimental ES module support in VM module.
.
.It Fl -experimental-wasi-unstable-preview1
Enable experimental WebAssembly System Interface support. This
flag is no longer required as WASI is enabled by default.
.
.It Fl -experimental-wasm-modules
Enable experimental WebAssembly module support.
.
.It Fl -force-context-aware
Disable loading native addons that are not context-aware.
.
.It Fl -force-fips
Force FIPS-compliant crypto on startup
(Cannot be disabled from script code).
Same requirements as
.Fl -enable-fips .
.
.It Fl -frozen-intrinsics
Enable experimental frozen intrinsics support.
.
.It Fl -heapsnapshot-near-heap-limit Ns = Ns Ar max_count
Generate heap snapshot when the V8 heap usage is approaching the heap limit.
No more than the specified number of snapshots will be generated.
.
.It Fl -heapsnapshot-signal Ns = Ns Ar signal
Generate heap snapshot on specified signal.
.
.It Fl -heap-prof
Start the V8 heap profiler on start up, and write the heap profile to disk
before exit. If
.Fl -heap-prof-dir
is not specified, the profile will be written to the current working directory
with a generated file name.
.
.It Fl -heap-prof-dir
The directory where the heap profiles generated by
.Fl -heap-prof
will be placed.
The default value is controlled by the
.Fl -diagnostic-dir .
command-line option.
.
.It Fl -heap-prof-interval
The average sampling interval in bytes for the heap profiles generated by
.Fl -heap-prof .
The default is
.Sy 512 * 1024 .
.
.It Fl -heap-prof-name
File name of the V8 heap profile generated with
.Fl -heap-prof .
.
.It Fl -icu-data-dir Ns = Ns Ar file
Specify ICU data load path.
Overrides
.Ev NODE_ICU_DATA .
.
.It Fl -input-type Ns = Ns Ar type
Set the module resolution type for input via --eval, --print or STDIN.
.
.It Fl -inspect-brk Ns = Ns Ar [host:]port
Activate inspector on
.Ar host:port
and break at start of user script.
.
.It Fl -inspect-port Ns = Ns Ar [host:]port
Set the
.Ar host:port
to be used when the inspector is activated.
.
.It Fl -inspect-publish-uid=stderr,http
Specify how the inspector WebSocket URL is exposed.
Valid values are
.Sy stderr
and
.Sy http .
Default is
.Sy stderr,http .
.
.It Fl -inspect Ns = Ns Ar [host:]port
Activate inspector on
.Ar host:port .
Default is
.Sy 127.0.0.1:9229 .
.Pp
V8 Inspector integration allows attaching Chrome DevTools and IDEs to Node.js instances for debugging and profiling.
It uses the Chrome DevTools Protocol.
.
.It Fl -insecure-http-parser
Use an insecure HTTP parser that accepts invalid HTTP headers. This may allow
interoperability with non-conformant HTTP implementations. It may also allow
request smuggling and other HTTP attacks that rely on invalid headers being
accepted. Avoid using this option.
.
.It Fl -jitless
Disable runtime allocation of executable memory. This may be required on
some platforms for security reasons. It can also reduce attack surface on
other platforms, but the performance impact may be severe.
.
.Pp
This flag is inherited from V8 and is subject to change upstream. It may
disappear in a non-semver-major release.
.
.It Fl -max-http-header-size Ns = Ns Ar size
Specify the maximum size of HTTP headers in bytes. Defaults to 16 KiB.
.
.It Fl -napi-modules
This option is a no-op.
It is kept for compatibility.
.
.It Fl -no-deprecation
Silence deprecation warnings.
.
.It Fl -no-extra-info-on-fatal-exception
Hide extra information on fatal exception that causes exit.
.
.It Fl -no-force-async-hooks-checks
Disable runtime checks for `async_hooks`.
These will still be enabled dynamically when `async_hooks` is enabled.
.
.It Fl -no-addons
Disable the `node-addons` exports condition as well as disable loading native
addons. When `--no-addons` is specified, calling `process.dlopen` or requiring
a native C++ addon will fail and throw an exception.
.
.It Fl -no-global-search-paths
Do not search modules from global paths.
.
.It Fl -no-warnings
Silence all process warnings (including deprecations).
.
.It Fl -node-memory-debug
Enable extra debug checks for memory leaks in Node.js internals. This is
usually only useful for developers debugging Node.js itself.
.
.It Fl -openssl-config Ns = Ns Ar file
Load an OpenSSL configuration file on startup.
Among other uses, this can be used to enable FIPS-compliant crypto if Node.js is built with
.Sy ./configure --openssl-fips .
.
.It Fl -pending-deprecation
Emit pending deprecation warnings.
.
.It Fl -policy-integrity Ns = Ns Ar sri
Instructs Node.js to error prior to running any code if the policy does not have the specified integrity. It expects a Subresource Integrity string as a parameter.
.
.It Fl -preserve-symlinks
Instructs the module loader to preserve symbolic links when resolving and caching modules other than the main module.
.
.It Fl -preserve-symlinks-main
Instructs the module loader to preserve symbolic links when resolving and caching the main module.
.
.It Fl -prof
Generate V8 profiler output.
.
.It Fl -prof-process
Process V8 profiler output generated using the V8 option
.Fl -prof .
.
.It Fl -redirect-warnings Ns = Ns Ar file
Write process warnings to the given
.Ar file
instead of printing to stderr.
.
.It Fl -report-compact
Write
.Sy diagnostic reports
in a compact format, single-line JSON.
.
.It Fl -report-dir Fl -report-directory
Location at which the
.Sy diagnostic report
will be generated.
The `file` name may be an absolute path. If it is not, the default directory it will
be written to is controlled by the
.Fl -diagnostic-dir .
command-line option.
.
.It Fl -report-filename
Name of the file to which the
.Sy diagnostic report
will be written.
.
.It Fl -report-on-fatalerror
Enables the
.Sy diagnostic report
to be triggered on fatal errors (internal errors within the Node.js runtime such
as out of memory) that leads to termination of the application. Useful to
inspect various diagnostic data elements such as heap, stack, event loop state,
resource consumption etc. to reason about the fatal error.
.
.It Fl -report-on-signal
Enables
.Sy diagnostic report
to be generated upon receiving the specified (or predefined) signal to the
running Node.js process. Default signal is SIGUSR2.
.
.It Fl -report-signal
Sets or resets the signal for
.Sy diagnostic report
generation (not supported on Windows). Default signal is SIGUSR2.
.
.It Fl -report-uncaught-exception
Enables
.Sy diagnostic report
to be generated on un-caught exceptions. Useful when inspecting JavaScript
stack in conjunction with native stack and other runtime environment data.
.
.It Fl -secure-heap Ns = Ns Ar n
Specify the size of the OpenSSL secure heap. Any value less than 2 disables
the secure heap. The default is 0. The value must be a power of two.
.
.It Fl -secure-heap-min Ns = Ns Ar n
Specify the minimum allocation from the OpenSSL secure heap. The default is 2. The value must be a power of two.
.
.It Fl -test
Starts the Node.js command line test runner.
.
.It Fl -test-concurrency
The maximum number of test files that the test runner CLI will execute
concurrently.
.
.It Fl -test-name-pattern
A regular expression that configures the test runner to only execute tests
whose name matches the provided pattern.
.
.It Fl -test-reporter
A test reporter to use when running tests.
.
.It Fl -test-reporter-destination
The destination for the corresponding test reporter.
.
.It Fl -test-only
Configures the test runner to only execute top level tests that have the `only`
option set.
.
.It Fl -test-shard
Test suite shard to execute in a format of <index>/<total>.

.It Fl -test-timeout
A number of milliseconds the test execution will fail after.
.
.It Fl -throw-deprecation
Throw errors for deprecations.
.
.It Fl -title Ns = Ns Ar title
Specify process.title on startup.
.
.It Fl -tls-cipher-list Ns = Ns Ar list
Specify an alternative default TLS cipher list.
Requires Node.js to be built with crypto support. (Default)
.
.It Fl -tls-keylog Ns = Ns Ar file
Log TLS key material to a file. The key material is in NSS SSLKEYLOGFILE
format and can be used by software (such as Wireshark) to decrypt the TLS
traffic.
.
.It Fl -tls-max-v1.2
Set default  maxVersion to 'TLSv1.2'. Use to disable support for TLSv1.3.
.
.It Fl -tls-max-v1.3
Set default  maxVersion to 'TLSv1.3'. Use to enable support for TLSv1.3.
.
.It Fl -tls-min-v1.0
Set default minVersion to 'TLSv1'. Use for compatibility with old TLS clients
or servers.
.
.It Fl -tls-min-v1.1
Set default minVersion to 'TLSv1.1'. Use for compatibility with old TLS clients
or servers.
.
.It Fl -tls-min-v1.2
Set default minVersion to 'TLSv1.2'. This is the default for 12.x and later,
but the option is supported for compatibility with older Node.js versions.
.
.It Fl -tls-min-v1.3
Set default minVersion to 'TLSv1.3'. Use to disable support for TLSv1.2 in
favour of TLSv1.3, which is more secure.
.
.It Fl -trace-atomics-wait
Print short summaries of calls to
.Sy Atomics.wait() .
.
This flag is deprecated.
.It Fl -trace-deprecation
Print stack traces for deprecations.
.
.It Fl -trace-event-categories Ar categories
A comma-separated list of categories that should be traced when trace event tracing is enabled using
.Fl -trace-events-enabled .
.
.It Fl -trace-event-file-pattern Ar pattern
Template string specifying the filepath for the trace event data, it
supports
.Sy ${rotation}
and
.Sy ${pid} .
.
.It Fl -trace-events-enabled
Enable the collection of trace event tracing information.
.
.It Fl -trace-exit
Prints a stack trace whenever an environment is exited proactively,
i.e. invoking `process.exit()`.
.It Fl -trace-sigint
Prints a stack trace on SIGINT.
.
.It Fl -trace-sync-io
Print a stack trace whenever synchronous I/O is detected after the first turn of the event loop.
.
.It Fl -trace-tls
Prints TLS packet trace information to stderr.
.
.It Fl -trace-uncaught
Print stack traces for uncaught exceptions; usually, the stack trace associated
with the creation of an
.Sy Error
is printed, whereas this makes Node.js also
print the stack trace associated with throwing the value (which does not need
to be an
.Sy Error
instance).
.Pp
Enabling this option may affect garbage collection behavior negatively.
.
.It Fl -trace-warnings
Print stack traces for process warnings (including deprecations).
.
.It Fl -track-heap-objects
Track heap object allocations for heap snapshots.
.
.It Fl -unhandled-rejections=mode
Define the behavior for unhandled rejections. Can be one of `strict` (raise an error), `warn` (enforce warnings) or `none` (silence warnings).
.
.It Fl -use-bundled-ca , Fl -use-openssl-ca
Use bundled Mozilla CA store as supplied by current Node.js version or use OpenSSL's default CA store.
The default store is selectable at build-time.
.Pp
The bundled CA store, as supplied by Node.js, is a snapshot of Mozilla CA store that is fixed at release time.
It is identical on all supported platforms.
.Pp
Using OpenSSL store allows for external modifications of the store.
For most Linux and BSD distributions, this store is maintained by the distribution maintainers and system administrators.
OpenSSL CA store location is dependent on configuration of the OpenSSL library but this can be altered at runtime using environment variables.
.Pp
See
.Ev SSL_CERT_DIR
and
.Ev SSL_CERT_FILE .
.
.It Fl -use-largepages Ns = Ns Ar mode
Re-map the Node.js static code to large memory pages at startup. If supported on
the target system, this will cause the Node.js static code to be moved onto 2
MiB pages instead of 4 KiB pages.
.Pp
.Ar mode
must have one of the following values:
`off` (the default value, meaning do not map), `on` (map and ignore failure,
reporting it to stderr), or `silent` (map and silently ignore failure).
.
.It Fl -v8-options
Print V8 command-line options.
.
.It Fl -v8-pool-size Ns = Ns Ar num
Set V8's thread pool size which will be used to allocate background jobs.
If set to 0 then V8 will choose an appropriate size of the thread pool based on the number of online processors.
If the value provided is larger than V8's maximum, then the largest value will be chosen.
.
.It Fl -zero-fill-buffers
Automatically zero-fills all newly allocated Buffer and SlowBuffer instances.
.
.It Fl c , Fl -check
Check the script's syntax without executing it.
Exits with an error code if script is invalid.
.
.It Fl e , Fl -eval Ar string
Evaluate
.Ar string
as JavaScript.
.
.It Fl h , Fl -help
Print command-line options.
The output of this option is less detailed than this document.
.
.It Fl i , Fl -interactive
Open the REPL even if stdin does not appear to be a terminal.
.
.It Fl p , Fl -print Ar string
Identical to
.Fl e ,
but prints the result.
.
.It Fl r , Fl -require Ar module
Preload the specified
.Ar module
at startup.
Follows `require()`'s module resolution rules.
.Ar module
may be either a path to a file, or a Node.js module name.
.
.It Fl v , Fl -version
Print node's version.
.El
.
.\" =====================================================================
.Sh ENVIRONMENT
.Bl -tag -width 6n
.It Ev FORCE_COLOR
Used to enable ANSI colorized output. The value may be one of:
.Ar 1
,
.Ar true
, or
.Ar an empty string
to
indicate 16-color support,
.Ar 2
to indicate 256-color support, or
.Ar 3
to indicate 16 million-color support. When used and set to a supported
value, both the NO_COLOR and NODE_DISABLE_COLORS environment variables
are ignored. Any other value will result in colorized output being
disabled.
.
.It Ev NO_COLOR
Alias for NODE_DISABLE_COLORS
.
.It Ev NODE_DEBUG Ar modules...
Comma-separated list of core modules that should print debug information.
.
.It Ev NODE_DEBUG_NATIVE Ar modules...
Comma-separated list of C++ core modules that should print debug information.
.
.It Ev NODE_DISABLE_COLORS
When set to
.Ar 1 ,
colors will not be used in the REPL.
.
.It Ev NODE_EXTRA_CA_CERTS Ar file
When set, the well-known
.Dq root
CAs (like VeriSign) will be extended with the extra certificates in
.Ar file .
The file should consist of one or more trusted certificates in PEM format.
.Pp
If
.Ar file
is missing or misformatted, a message will be emitted once using
.Sy process.emitWarning() ,
but any errors are otherwise ignored.
.Pp
This environment variable is ignored when `node` runs as setuid root or
has Linux file capabilities set.
.Pp
The
.Ar NODE_EXTRA_CA_CERTS
environment variable is only read when the Node.js process is first launched.
Changing the value at runtime using
.Ar process.env.NODE_EXTRA_CA_CERTS
has no effect on the current process.
.
.It Ev NODE_ICU_DATA Ar file
Data path for ICU (Intl object) data.
Will extend linked-in data when compiled with small-icu support.
.
.It Ev NODE_NO_WARNINGS
When set to
.Ar 1 ,
process warnings are silenced.
.
.It Ev NODE_OPTIONS Ar options...
A space-separated list of command-line
.Ar options ,
which are interpreted as if they had been specified on the command line before the actual command (so they can be overridden).
Node.js will exit with an error if an option that is not allowed in the environment is used, such as
.Fl -print
or a script file.
.
.It Ev NODE_PATH Ar directories...
A colon-separated list of
.Ar directories
prefixed to the module search path.
.
.It Ev NODE_PENDING_DEPRECATION
When set to
.Ar 1 ,
emit pending deprecation warnings.
.
.It Ev NODE_PRESERVE_SYMLINKS
When set to
.Ar 1 ,
the module loader preserves symbolic links when resolving and caching modules.
.
.It Ev NODE_REDIRECT_WARNINGS Ar file
Write process warnings to the given
.Ar file
instead of printing to stderr.
Equivalent to passing
.Fl -redirect-warnings Ar file
on the command line.
.
.It Ev NODE_REPL_HISTORY Ar file
Path to the
.Ar file
used to store persistent REPL history.
The default path is
.Sy ~/.node_repl_history ,
which is overridden by this variable.
Setting the value to an empty string ("" or " ") will disable persistent REPL history.
.
.It Ev NODE_REPL_EXTERNAL_MODULE Ar file
Path to a Node.js module which will be loaded in place of the built-in REPL.
Overriding this value to an empty string (`''`) will use the built-in REPL.
.
.It Ev NODE_SKIP_PLATFORM_CHECK
When set to
.Ar 1 ,
the check for a supported platform is skipped during Node.js startup.
Node.js might not execute correctly.
Any issues encountered on unsupported platforms will not be fixed.
.
.It Ev NODE_TLS_REJECT_UNAUTHORIZED
When set to
.Ar 0 ,
TLS certificate validation is disabled.
.
.It Ev NODE_V8_COVERAGE Ar dir
When set, Node.js writes JavaScript code coverage information to
.Ar dir .
.
.It Ev OPENSSL_CONF Ar file
Load an OpenSSL configuration file on startup.
Among other uses, this can be used to enable FIPS-compliant crypto if Node.js is built with
.Sy ./configure --openssl-fips .
.Pp
If the
.Fl -openssl-config
command-line option is used, this environment variable is ignored.
.
.It Ev SSL_CERT_DIR Ar dir
If
.Fl -use-openssl-ca
is enabled, this overrides and sets OpenSSL's directory containing trusted certificates.
.
.It Ev SSL_CERT_FILE Ar file
If
.Fl -use-openssl-ca
is enabled, this overrides and sets OpenSSL's file containing trusted certificates.
.
.It Ev TZ
Specify the timezone configuration.
.
.It Ev UV_THREADPOOL_SIZE Ar size
Sets the number of threads used in libuv's threadpool to
.Ar size .
.
.El
.\"=====================================================================
.Sh BUGS
Bugs are tracked in GitHub Issues:
.Sy https://github.com/nodejs/node/issues
.
.\"======================================================================
.Sh COPYRIGHT
Copyright Node.js contributors.
Node.js is available under the MIT license.
.
.Pp
Node.js also includes external libraries that are available under a variety of licenses.
See
.Sy https://github.com/nodejs/node/blob/HEAD/LICENSE
for the full license text.
.
.\"======================================================================
.Sh SEE ALSO
Website:
.Sy https://nodejs.org/
.
.Pp
Documentation:
.Sy https://nodejs.org/api/
.
.Pp
GitHub repository and issue tracker:
.Sy https://github.com/nodejs/node
.
.Pp
IRC (general questions):
.Sy "libera.chat #node.js"
(unofficial)
.
.\"======================================================================
.Sh AUTHORS
Written and maintained by 1000+ contributors:
.Sy https://github.com/nodejs/node/blob/HEAD/AUTHORS
