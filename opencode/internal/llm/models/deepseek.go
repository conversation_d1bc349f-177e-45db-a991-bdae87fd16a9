package models

const (
	DeepSeekChat     ModelID = "deepseek-chat"
	DeepSeekReasoner ModelID = "deepseek-reasoner"
)

var DeepSeekModels = map[ModelID]Model{
	DeepSeekChat: {
		ID:                  DeepSeekChat,
		Name:                "DeepSeek Chat (V3-0324)",
		Provider:            ProviderDeepSeek,
		APIModel:            "deepseek-chat",
		CostPer1MIn:         0.27,
		CostPer1MInCached:   0.07,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        1.10,
		ContextWindow:       64_000,
		DefaultMaxTokens:    4_000,
		SupportsAttachments: true,
	},
	DeepSeekReasoner: {
		ID:                  DeepSeekReasoner,
		Name:                "DeepSeek Reasoner (R1-0528)",
		Provider:            ProviderDeepSeek,
		APIModel:            "deepseek-reasoner",
		CostPer1MIn:         0.55,
		CostPer1MInCached:   0.14,
		CostPer1MOutCached:  0.0,
		CostPer1MOut:        2.19,
		ContextWindow:       64_000,
		DefaultMaxTokens:    32_000,
		CanReason:           true,
		SupportsAttachments: true,
	},
}
